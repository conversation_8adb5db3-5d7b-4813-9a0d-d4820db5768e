# DevDelta Tech - Company Website

A professional website for DevDelta Tech, a software development company that builds custom solutions for businesses.

## 🌟 Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Multiple Pages**: Home, About, Services, and Contact pages
- **Interactive Elements**: Contact form, mobile navigation, and smooth scrolling
- **SEO Optimized**: Proper meta tags and semantic HTML structure
- **Fast Loading**: Optimized images and efficient CSS/JavaScript

## 📁 Project Structure

```
devdeltatech/
├── index.html          # Homepage
├── about.html          # About Us page
├── services.html       # Services page
├── contact.html        # Contact page
├── css/
│   └── style.css       # Main stylesheet
├── js/
│   └── main.js         # JavaScript functionality
├── images/             # All images and graphics
│   ├── devdeltatech-logo.svg
│   ├── hero-illustration.svg
│   └── ...
└── README.md           # This file
```

## 🚀 GitHub Pages Deployment

### Method 1: Using GitHub Web Interface

1. **Create a new repository** on GitHub:
   - Go to [GitHub](https://github.com) and sign in
   - Click "New repository"
   - Name it `devdeltatech` (or any name you prefer)
   - Make sure it's **Public**
   - Don't initialize with README (we already have one)

2. **Upload your files**:
   - Click "uploading an existing file"
   - Drag and drop all files and folders from this project
   - Commit the changes

3. **Enable GitHub Pages**:
   - Go to repository Settings
   - Scroll down to "Pages" section
   - Under "Source", select "Deploy from a branch"
   - Choose "main" branch and "/ (root)" folder
   - Click "Save"

4. **Access your website**:
   - Your site will be available at: `https://yourusername.github.io/devdeltatech`
   - It may take a few minutes to deploy

### Method 2: Using Git Commands

```bash
# Initialize git repository
git init

# Add all files
git add .

# Commit files
git commit -m "Initial commit: DevDelta Tech website"

# Add remote repository (replace with your GitHub repo URL)
git remote add origin https://github.com/yourusername/devdeltatech.git

# Push to GitHub
git push -u origin main
```

Then follow steps 3-4 from Method 1 to enable GitHub Pages.

## 🛠️ Customization

### Update Company Information

1. **Contact Details**: Edit contact information in all HTML files
2. **Company Description**: Update text content in `index.html` and `about.html`
3. **Services**: Modify services in `services.html`
4. **Team Members**: Update team information in `about.html`

### Styling Changes

- **Colors**: Modify CSS variables in `css/style.css`
- **Fonts**: Change font imports in HTML files
- **Layout**: Adjust grid layouts and spacing in CSS

### Adding New Pages

1. Create new HTML file following the existing structure
2. Update navigation in all HTML files
3. Add corresponding styles in `css/style.css`

## 📱 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## 🔧 Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with Flexbox and Grid
- **JavaScript**: Interactive functionality
- **Font Awesome**: Icons
- **Google Fonts**: Typography (Inter font family)

## 📞 Support

For any questions or support regarding this website template, please contact:
- Email: <EMAIL>
- Phone: +****************

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

**DevDelta Tech** - Transforming businesses through innovative software solutions.
