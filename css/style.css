/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo {
    height: 40px;
    width: 40px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #667eea;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: #333;
}

.highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #666;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.hero-image img {
    width: 100%;
    height: auto;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Services Preview */
.services-preview {
    padding: 80px 0;
    background: #f8f9fa;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.service-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.service-card ul {
    list-style: none;
    margin-top: 1rem;
}

.service-card li {
    padding: 0.5rem 0;
    color: #666;
    position: relative;
    padding-left: 1.5rem;
}

.service-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.services-cta {
    text-align: center;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.cta .btn-primary {
    background: white;
    color: #667eea;
}

.cta .btn-primary:hover {
    background: #f8f9fa;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: white;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Page Header */
.page-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.page-header h1 {
    color: white;
    margin-bottom: 1rem;
}

.page-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
}

/* About Page Styles */
.about-content {
    padding: 80px 0;
}

.about-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.about-text h3 {
    color: #333;
    margin: 2rem 0 1rem;
}

.about-list {
    list-style: none;
    margin: 1rem 0;
}

.about-list li {
    padding: 0.5rem 0;
    color: #666;
    position: relative;
    padding-left: 1.5rem;
}

.about-list li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.about-image img {
    width: 100%;
    border-radius: 12px;
}

/* Mission & Vision */
.mission-vision {
    padding: 80px 0;
    background: #f8f9fa;
}

.mv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.mv-card {
    background: white;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.mv-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.mv-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

/* Values Section */
.values {
    padding: 80px 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    text-align: center;
    padding: 2rem;
}

.value-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.value-card h4 {
    color: #333;
    margin-bottom: 1rem;
}

/* Team Section */
.team {
    padding: 80px 0;
    background: #f8f9fa;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.team-member {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.member-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    background: #ddd;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.member-role {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: #666;
    font-size: 0.9rem;
}

/* Services Page Styles */
.services-overview {
    padding: 60px 0;
    text-align: center;
}

.overview-content {
    max-width: 800px;
    margin: 0 auto;
}

.service-detail {
    padding: 80px 0;
}

.service-detail.alt {
    background: #f8f9fa;
}

.service-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.service-detail.alt .service-content {
    grid-template-columns: 1fr 1fr;
}

.service-text h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.service-text h4 {
    color: #333;
    margin: 2rem 0 1rem;
}

.service-text ul {
    list-style: none;
    margin: 1rem 0;
}

.service-text li {
    padding: 0.5rem 0;
    color: #666;
    position: relative;
    padding-left: 1.5rem;
}

.service-text li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #667eea;
    font-weight: bold;
}

.service-image img {
    width: 100%;
    border-radius: 12px;
}

.tech-tags, .industry-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.tech-tag, .industry-tag {
    background: #667eea;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.industry-tag {
    background: #764ba2;
}

/* Process Section */
.process {
    padding: 80px 0;
    background: #f8f9fa;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.process-step {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.process-step h4 {
    color: #333;
    margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .about-grid,
    .service-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mv-grid {
        grid-template-columns: 1fr;
    }

    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
}

/* Contact Page Styles */
.contact-section {
    padding: 80px 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.contact-form-container h2 {
    color: #333;
    margin-bottom: 1rem;
}

.contact-form-container p {
    margin-bottom: 2rem;
    color: #666;
}

.contact-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-info {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    height: fit-content;
}

.contact-info h3 {
    color: #333;
    margin-bottom: 1rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: #666;
    margin-bottom: 0.25rem;
}

.social-links {
    margin-top: 2rem;
}

.social-links h4 {
    color: #333;
    margin-bottom: 1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-2px);
}

/* FAQ Section */
.faq-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.faq-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-item h4 {
    color: #333;
    margin-bottom: 1rem;
}

.faq-item p {
    color: #666;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .contact-info {
        padding: 1.5rem;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .tech-tags,
    .industry-tags {
        justify-content: center;
    }

    .social-icons {
        justify-content: center;
    }
}
